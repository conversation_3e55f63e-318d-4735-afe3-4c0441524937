# avros

Centralized repository for Avro's Schema on Cloudbeds


## How to guide

1. **templates**: This folder contains the templates that will be used to push to the avros-language repo for example for java there is a [pom file](./templates/java/avro-pom.xml) that contains the plugin that will build the avros and push it to avros-java and contains also the pom.xml for the avros-java

2. **cloudbeds**: This folder contains all the avro files that exist on cloudbeds. The namespace being com.cloudbeds is important because the plugin on Java only works for that group id, probably there is configuration to update this. Since right now there is only one language implemented this is not a problem but we would need to see how the namespace can affect the plugin for the other languages

3. **.github**: This folder contains the script that will build the classes and push it to the repos