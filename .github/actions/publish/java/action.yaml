name: 'Publish Java artifacts'
description: Action to publish Java sources and artifacts to GH Packages
inputs:
  github-token:
    description: 'The token to used to clone and interact with Github repositories.'
    required: true
  maven-token:
    description: 'The token to used to publish to maven.'
    required: true
  cicd-user-name:
    description: 'The name of the user used to create any commits / tags.'
    default: 'cloudbeds-ci'
    required: false
  cicd-user-email:
    description: 'The user of the user used to create any commits.'
    default: '<EMAIL>'
    required: false

runs:
  using: composite
  steps:
    - name: checkout java avros
      uses: actions/checkout@v4
      with:
        repository: cloudbeds/avros-java
        path: avros-java
        token: ${{ inputs.github-token }}
        fetch-depth: 0

    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: maven

    - uses: dorny/paths-filter@v3
      id: changes
      with:
        token: ${{ inputs.github-token }}
        list-files: 'csv'
        filters: |
          avros:
            - 'cloudbeds/**'
          version:
            - 'VERSION.txt'
            - 'cloudbeds/**/VERSION.txt'

    - name: Build java classes from Avro schemas
      if: steps.changes.outputs.avros == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        echo "Building Java classes from Avro schemas"

        mkdir -p avros-workspace/src/main/avro
        cp templates/java/avro-pom.xml avros-workspace/pom.xml
        cp -R cloudbeds avros-workspace/src/main/avro
        cd avros-workspace

        mvn clean compile

        # Check if the target directory was created
        if [ ! -d "target/generated-sources/avro" ]; then
            echo "Error: Maven build did not generate any sources. Check the Maven logs above."
            exit 1
        fi

        echo "Finish building sources"

    - name: Publish artifacts to avros-java repo and GH packages for Maven
      if: steps.changes.outputs.avros == 'true' || steps.changes.outputs.version == 'true'
      shell: bash
      run: |
        echo "Starting pushing to github"
        VERSION=$(<VERSION.txt)
        SHORT_SHA="${GITHUB_SHA:0:7}"
        DEST_REPO=./avros-java
        mkdir -p $DEST_REPO/src/main/java

        # Copy generated Java classes to destination repository
        rsync -av --delete ./avros-workspace/target/generated-sources/avro/com/cloudbeds $DEST_REPO/src/main/java/com/

        cp ./templates/java/pom.xml $DEST_REPO/pom.xml
        cp ./templates/java/settings.xml $HOME/.m2/settings.xml

        echo "Local build and rsync complete, Starting push to github"

        pushd $DEST_REPO
        git config --global user.name ${{ inputs.cicd-user-name }}
        git config --global user.email ${{ inputs.cicd-user-email }}
        git remote set-url origin https://x-access-token:${{ inputs.github-token }}@github.com/cloudbeds/avros-java

        git add -N .
        if ! git diff --exit-code > /dev/null; then
            # If we are not on main, then append <branch>-SNAPSHOT to the last version and push. Skip commit to main.
            if [[ "$GITHUB_REF_NAME" != "main" ]]; then
                VERSION="${VERSION}-${SHORT_SHA}-SNAPSHOT"
            fi

            MAVEN_VERSION="${VERSION:1}" # remove leading v per maven
            echo "Deploying version ${MAVEN_VERSION}"

            mvn versions:set -DnewVersion=${MAVEN_VERSION}
            mvn deploy -Dgithub.username=${{ inputs.cicd-user-email }} -Dgithub.token=${{ inputs.maven-token }} -Dmaven.resolver.transport=wagon

            if [[ "$GITHUB_REF_NAME" == "main" ]]; then
                echo "Pushing to HEAD"
                git add .
                git commit -m "Release of avros ${VERSION}"
                git push origin HEAD
                git tag ${VERSION}
                git push origin ${VERSION}
                git push origin --delete ${DEV_BRANCH} 2>/dev/null || true
            else
                DEV_BRANCH="$GITHUB_REF_NAME-cicd"
                echo "Pushing to develop branch"

                # For non-main branches, create a branch on the avros-php remote and push changes there.
                if ! git rev-parse --verify ${DEV_BRANCH}; then
                    echo "Creating branch ${DEV_BRANCH}"
                    git branch ${DEV_BRANCH}
                fi

                git checkout ${DEV_BRANCH}

                git add .
                if ! git diff --cached --exit-code > /dev/null; then
                  git commit -m "Update of avros $GITHUB_REF_NAME"
                fi

                if git ls-remote --heads --exit-code origin ${DEV_BRANCH}; then
                    echo "Branch $DEV_BRANCH exists on remote, pulling latest changes"
                    git fetch origin ${DEV_BRANCH}
                    git merge -s recursive -X ours origin/${DEV_BRANCH}
                fi

                git push origin ${DEV_BRANCH}
            fi
        else
            echo "No changes detected for $DEST_REPO"
        fi
        popd
