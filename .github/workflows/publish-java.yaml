name: Publish Java

on:
  workflow_call:

jobs:
  publish-java:
    name: Publish java artifacts
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout <PERSON><PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Get gh app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          app_id: 1118430
          aws_role_arn: arn:aws:iam::048781935247:role/GH-APP-OIDC-CBLibGenerator
          aws_ssm_param_name: /github/app/CBLibGenerator/private-key

      - name: Publish Java artifacts
        uses: ./.github/actions/publish/java
        with:
          github-token: ${{ steps.gh-app-token.outputs.github-token }}
          maven-token: ${{ secrets.CB_CI_MAVEN_GH_PACKAGES_WRITE }}
