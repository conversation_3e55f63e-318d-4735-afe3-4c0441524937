name: Pull Request

on:
  workflow_dispatch:
  pull_request:

jobs:
  check-versions:
    name: Validate versioning
    runs-on: x1-core
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Fetch the main branch for comparison
      - name: Fetch main branch
        run: git fetch origin main:main

      # Compare versions
      - name: Compare version with main
        run: |
          # Get the version from the current branch
          current_version=$(cat VERSION.txt)

          # Get the version from the main branch
          main_version=$(git show main:VERSION.txt)

          echo "Current version: $current_version"
          echo "Main version: $main_version"

          # Compare versions
          if [ "$current_version" = "$main_version" ]; then
            echo "Error: Version in current branch is not higher than the version in main"
            exit 1
          fi

          # Split the versions into their numeric components
          IFS='.' read -r -a current_parts <<< "$current_version"
          IFS='.' read -r -a main_parts <<< "$main_version"

          # Compare version parts
          for i in {0..2}; do
            if [[ ${current_parts[i]} -gt ${main_parts[i]} ]]; then
              break
            elif [[ ${current_parts[i]} -lt ${main_parts[i]} ]]; then
              echo "Error: Version in current branch is not higher than the version in main"
              exit 1
            fi
          done

      - name: Success
        run: echo "Version check passed, current version is higher than main version."

  publish-java:
    uses: ./.github/workflows/publish-java.yaml
    secrets: inherit
