{"fields": [{"name": "id", "type": "long"}, {"name": "status", "type": {"name": "AccountsReceivableLedgerStatus", "type": "enum", "symbols": ["OPEN", "CLOSED"]}}, {"name": "property_id", "type": "long"}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "created_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "updated_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}], "name": "AccountsReceivableLedgerValue", "namespace": "com.cloudbeds.AccountingService", "type": "record"}