{"fields": [{"name": "booking_id", "type": "long"}, {"name": "type", "type": {"name": "Type", "type": "enum", "symbols": ["TRANSACTION", "BOOKING"]}}, {"name": "transaction", "default": null, "type": ["null", {"name": "AccountReceivableTransaction", "type": "record", "fields": [{"name": "transaction_id", "type": "string"}, {"name": "folio_transaction_id", "default": null, "type": ["null", "long"]}, {"name": "is_routed", "default": null, "type": ["null", "boolean"]}, {"name": "updated_datetime", "default": null, "type": ["null", {"connect.default": 0, "connect.name": "io.debezium.time.ZonedTimestamp", "connect.version": 1, "type": "long"}]}]}]}, {"name": "booking", "default": null, "type": ["null", {"name": "AccountReceivableBooking", "type": "record", "fields": [{"name": "booking_id", "type": "long"}, {"name": "identifier", "type": ["null", "string"]}, {"name": "property_id", "type": "long"}, {"name": "customer_id", "type": "long"}, {"name": "customer_name", "type": "string"}, {"name": "updated_at", "type": "string"}, {"name": "is_closed", "type": "boolean"}, {"name": "is_deleted", "type": "boolean"}, {"name": "currency", "type": "string"}]}]}], "name": "GeneratedTransactionEventValue", "namespace": "com.cloudbeds.AccountingService", "type": "record"}