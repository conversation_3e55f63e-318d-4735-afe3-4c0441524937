{
  "fields": [
    {
      "name": "source_id",
      "type": "long"
    },
    {
      "name": "property_id",
      "default": 0,
      "type": "long"
    },
    {
      "name": "source_kind",
      "type": {
        "name": "MfdTransactionSourceKind",
        "type": "enum",
        "symbols": [
          "RESERVATION",
          "GROUP_PROFILE",
          "HOUSE_ACCOUNT",
          "CITY_LEDGER",
          "ACCOUNTS_RECEIVABLE_LEDGER"
        ]
      }
    },
    {
      "name": "type",
      "type": {
        "name": "TransactionEventType",
        "type": "enum",
        "symbols": [
          "MFD_TRANSACTION_EVENT",
          "CITY_LEDGER_EVENT",
          "ACCOUNTS_RECEIVABLE_EVENT",
          "RESERVATION_EVENT",
          "ROOM_EVENT",
          "ROOM_REVENUE_EVENT",
          "ADDON_EVENT",
          "ROUTED_EVENT",
          "ITEM_EVENT",
          "ADJUSTMENT_EVENT",
          "MANUAL_TAX_EVENT",
          "MANUAL_FEE_EVENT",
          "CUSTOM_ITEM_EVENT",
          "VOID_EVENT",
          "DELETE_EVENT",
          "RESERVATION_STATUS_EVENT",
          "PAYMENT_CREATED_EVENT",
          "PAYMENT_UPDATED_EVENT",
          "PAYMENT_CAPTURED_EVENT",
          "PAYMENT_CAPTURE_UPDATED",
          "NIGHT_AUDIT_EVENT",
          "REFUND_CREATED_EVENT",
          "REFUND_UPDATED_EVENT",
          "PAYMENT_VOIDED_EVENT",
          "ADDON_POSTED_EVENT",
          "PAYMENT_CHARGEBACK_CREATED_EVENT",
          "PAYMENT_CHARGEBACK_UPDATED_EVENT",
          "PAYMENT_ADJUSTMENT_EVENT",
          "PAYMENT_CHARGEBACK_REVERSAL_CREATED_EVENT",
          "PAYMENT_CHARGEBACK_REVERSAL_UPDATED_EVENT",
          "ROUTED_PENDING_TRANSACTION_EVENT",
          "MOVED_PENDING_TRANSACTION_EVENT",
          "BALANCE_TRANSFER_ACCOUNTS_RECEIVABLE_EVENT",
          "BALANCE_TRANSFER_UNDO_ACCOUNTS_RECEIVABLE_EVENT",
          "TRANSFER_DEPOSIT_EVENT",
          "MOVE_BETWEEN_FOLIOS_EVENT"
        ]
      }
    },
    {
      "name": "mfd_transaction_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "type",
              "type": {
                "name": "MfdTransactionEventType",
                "type": "enum",
                "symbols": [
                  "TRANSACTION",
                  "ROUTING"
                ]
              }
            },
            {
              "name": "transaction",
              "default": null,
              "type": [
                "null",
                {
                  "name": "MfdTransactionEvent",
                  "type": "record",
                  "fields": [
                    {
                      "name": "id",
                      "type": "string"
                    },
                    {
                      "name": "property_id",
                      "type": "long"
                    },
                    {
                      "name": "datetime_created",
                      "type": {
                        "connect.name": "org.apache.kafka.connect.data.Timestamp",
                        "connect.version": 1,
                        "logicalType": "timestamp-millis",
                        "type": "long"
                      }
                    },
                    {
                      "default": 0,
                      "name": "datetime_updated",
                      "type": {
                        "connect.default": 0,
                        "connect.name": "org.apache.kafka.connect.data.Timestamp",
                        "connect.version": 1,
                        "logicalType": "timestamp-millis",
                        "type": "long"
                      }
                    },
                    {
                      "name": "datetime_post",
                      "type": {
                        "connect.name": "org.apache.kafka.connect.data.Timestamp",
                        "connect.version": 1,
                        "logicalType": "timestamp-millis",
                        "type": "long"
                      }
                    },
                    {
                      "name": "datetime_transaction",
                      "type": {
                        "connect.name": "org.apache.kafka.connect.data.Timestamp",
                        "connect.version": 1,
                        "logicalType": "timestamp-millis",
                        "type": "long"
                      }
                    },
                    {
                      "name": "user_id",
                      "type": "long"
                    },
                    {
                      "name": "user_name",
                      "type": "string"
                    },
                    {
                      "name": "room_id",
                      "type": "string"
                    },
                    {
                      "name": "room_name",
                      "type": "string"
                    },
                    {
                      "name": "reservation_name",
                      "type": "string"
                    },
                    {
                      "name": "reservation_identifier",
                      "type": "string"
                    },
                    {
                      "default": "0",
                      "name": "res_room_identifier",
                      "type": {
                        "connect.default": "0",
                        "type": "string"
                      }
                    },
                    {
                      "default": null,
                      "name": "term_code",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "name": "description",
                      "type": "string"
                    },
                    {
                      "name": "notes",
                      "type": "string"
                    },
                    {
                      "default": 1,
                      "name": "qty",
                      "type": {
                        "connect.default": 1,
                        "type": "int"
                      }
                    },
                    {
                      "default": 0,
                      "name": "debit",
                      "type": {
                        "connect.default": 0,
                        "type": "double"
                      }
                    },
                    {
                      "default": 0,
                      "name": "debit_money",
                      "type": {
                        "connect.default": 0,
                        "type": "double"
                      }
                    },
                    {
                      "default": 0,
                      "name": "debit_scale",
                      "type": {
                        "connect.default": 0,
                        "connect.type": "int16",
                        "type": "int"
                      }
                    },
                    {
                      "default": 0,
                      "name": "credit",
                      "type": {
                        "connect.default": 0,
                        "type": "double"
                      }
                    },
                    {
                      "default": 0,
                      "name": "credit_money",
                      "type": {
                        "connect.default": 0,
                        "type": "double"
                      }
                    },
                    {
                      "default": 0,
                      "name": "credit_scale",
                      "type": {
                        "connect.default": 0,
                        "connect.type": "int16",
                        "type": "int"
                      }
                    },
                    {
                      "default": "$",
                      "name": "currency",
                      "type": {
                        "connect.default": "$",
                        "type": "string"
                      }
                    },
                    {
                      "default": false,
                      "name": "posted",
                      "type": "boolean"
                    },
                    {
                      "default": false,
                      "name": "void",
                      "type": "boolean"
                    },
                    {
                      "name": "type",
                      "type": "string"
                    },
                    {
                      "default": false,
                      "name": "deleted",
                      "type": "boolean"
                    },
                    {
                      "default": null,
                      "name": "reservation_room_id",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": null,
                      "name": "parent_id",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "name": "reservation_id",
                      "type": "long"
                    },
                    {
                      "default": null,
                      "name": "link_id",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "default": null,
                      "name": "source_type",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "default": null,
                      "name": "source_id",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "default": null,
                      "name": "description_filter",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "default": null,
                      "name": "void_id",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "default": null,
                      "name": "adjust_root_id",
                      "type": [
                        "null",
                        "string"
                      ]
                    },
                    {
                      "default": null,
                      "name": "house_account_id",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": null,
                      "name": "group_profile_id",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": null,
                      "name": "allotment_block_id",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": null,
                      "name": "is_private_ha",
                      "type": [
                        "null",
                        {
                          "connect.type": "int16",
                          "type": "int"
                        }
                      ]
                    },
                    {
                      "default": null,
                      "name": "drawer_id",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": null,
                      "name": "drawer_action_id",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": null,
                      "name": "customer_id",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": false,
                      "name": "is_refund",
                      "type": "boolean"
                    },
                    {
                      "default": "completed",
                      "name": "state",
                      "type": {
                        "connect.default": "completed",
                        "type": "string"
                      }
                    },
                    {
                      "default": false,
                      "name": "readonly",
                      "type": "boolean"
                    }
                  ]
                }
              ]
            },
            {
              "name": "routing",
              "default": null,
              "type": [
                "null",
                {
                  "name": "MfdTransactionRoutingEvent",
                  "type": "record",
                  "fields": [
                    {
                      "name": "id",
                      "type": "long"
                    },
                    {
                      "name": "transaction_id",
                      "type": "string"
                    },
                    {
                      "name": "source_id",
                      "type": "long"
                    },
                    {
                      "name": "source_kind",
                      "type": "MfdTransactionSourceKind"
                    }
                  ]
                }
              ]
            }
          ],
          "name": "MfdTransactionEventValue",
          "type": "record"
        }
      ]
    },
    {
      "name": "city_ledger_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "datetime_created",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "balance",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "default": false,
              "name": "void",
              "type": "boolean"
            },
            {
              "name": "city_ledger_id",
              "type": "long"
            },
            {
              "name": "external_relation_id",
              "type": "string"
            }
          ],
          "name": "CityLedgerEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "accounts_receivable_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "datetime_created",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "balance",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "default": false,
              "name": "void",
              "type": "boolean"
            },
            {
              "name": "accounts_receivable_ledger_id",
              "type": "long"
            },
            {
              "name": "external_relation_id",
              "type": "string"
            }
          ],
          "name": "AccountsReceivableEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "reservation_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "status",
              "type": {
                "name": "ReservationStatus",
                "type": "enum",
                "symbols": [
                  "CONFIRMED",
                  "CANCELED",
                  "CHECKED_IN",
                  "CHECKED_OUT",
                  "NOT_CONFIRMED",
                  "NO_SHOW",
                  "PAYPAL_INIT",
                  "PENDING_PAYMENT",
                  "IN_PROGRESS",
                  "CALL_TO_CONFIRM"
                ]
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "is_update",
              "type": "boolean"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "checkin_date",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Date",
                "connect.version": 1,
                "logicalType": "date",
                "type": "int"
              }
            },
            {
              "name": "checkout_date",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Date",
                "connect.version": 1,
                "logicalType": "date",
                "type": "int"
              }
            },
            {
              "name": "created_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "updated_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "taxes",
              "type": {
                "type": "array",
                "items": {
                  "fields": [
                    {
                      "name": "id",
                      "type": "string"
                    },
                    {
                      "name": "description",
                      "type": "string"
                    },
                    {
                      "name": "amount",
                      "type": "long"
                    },
                    {
                      "name": "currency",
                      "type": "string"
                    },
                    {
                      "name": "parent_id",
                      "type": "long"
                    },
                    {
                      "name": "parent_kind",
                      "type": [
                        "null",
                        {
                          "name": "ParentKind",
                          "type": "enum",
                          "symbols": [
                            "FEE"
                          ]
                        }
                      ]
                    }
                  ],
                  "name": "Tax",
                  "type": "record"
                }
              }
            },
            {
              "name": "fees",
              "type": {
                "type": "array",
                "items": {
                  "fields": [
                    {
                      "name": "id",
                      "type": "string"
                    },
                    {
                      "name": "description",
                      "type": "string"
                    },
                    {
                      "name": "amount",
                      "type": "long"
                    },
                    {
                      "name": "currency",
                      "type": "string"
                    }
                  ],
                  "name": "Fee",
                  "type": "record"
                }
              }
            },
            {
              "name": "last_night_audit_date",
              "default": null,
              "type": [
                "null",
                {
                  "connect.name": "org.apache.kafka.connect.data.Date",
                  "connect.version": 1,
                  "logicalType": "date",
                  "type": "int"
                }
              ]
            }
          ],
          "name": "ReservationEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "room_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "is_update",
              "type": "boolean"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "checkin_date",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Date",
                "connect.version": 1,
                "logicalType": "date",
                "type": "int"
              }
            },
            {
              "name": "checkout_date",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Date",
                "connect.version": 1,
                "logicalType": "date",
                "type": "int"
              }
            },
            {
              "name": "room_id",
              "type": "string"
            },
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "room_type_id",
              "type": "string"
            },
            {
              "name": "rate_name",
              "type": "string"
            },
            {
              "name": "created_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "updated_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "transaction_time",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "time-millis",
                "type": "int"
              }
            },
            {
              "name": "status",
              "type": {
                "name": "ReservationRoomStatus",
                "type": "enum",
                "symbols": [
                  "PENDING",
                  "CHECKED_IN",
                  "CHECKED_OUT"
                ]
              }
            },
            {
              "name": "nights",
              "type": {
                "type": "array",
                "items": {
                  "fields": [
                    {
                      "name": "date",
                      "type": {
                        "connect.name": "org.apache.kafka.connect.data.Date",
                        "connect.version": 1,
                        "logicalType": "date",
                        "type": "int"
                      }
                    },
                    {
                      "name": "amount",
                      "type": [
                        "null",
                        "long"
                      ]
                    },
                    {
                      "default": "USD",
                      "name": "currency",
                      "type": {
                        "connect.default": "USD",
                        "type": "string"
                      }
                    },
                    {
                      "name": "fees",
                      "type": {
                        "type": "array",
                        "items": "com.cloudbeds.AccountingService.Fee"
                      }
                    },
                    {
                      "name": "taxes",
                      "type": {
                        "type": "array",
                        "items": "com.cloudbeds.AccountingService.Tax"
                      }
                    }
                  ],
                  "name": "ReservationRoomNight",
                  "type": "record"
                }
              }
            },
            {
              "name": "reservation_status",
              "default": "CONFIRMED",
              "type": "com.cloudbeds.AccountingService.ReservationStatus"
            },
            {
              "name": "last_night_audit_date",
              "default": null,
              "type": [
                "null",
                {
                  "connect.name": "org.apache.kafka.connect.data.Date",
                  "connect.version": 1,
                  "logicalType": "date",
                  "type": "int"
                }
              ]
            },
            {
              "default": false,
              "name": "is_reset",
              "type": "boolean"
            }
          ],
          "name": "ReservationRoomEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "addon_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "is_update",
              "type": "boolean"
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "amount",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "name": "fees",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Fee"
              }
            },
            {
              "name": "taxes",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Tax"
              }
            },
            {
              "name": "product_id",
              "type": "long"
            },
            {
              "name": "booking_room_id",
              "type": "long"
            },
            {
              "name": "posted",
              "type": "boolean"
            },
            {
              "name": "addon_id",
              "default": 0,
              "type": "long"
            }
          ],
          "name": "AddonEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "routed_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "routed_to_source_id",
              "type": "long"
            },
            {
              "name": "routed_to_source_kind",
              "default": null,
              "type": [
                "null",
                "MfdTransactionSourceKind"
              ]
            },
            {
              "name": "transaction_ids",
              "type": {
                "type": "array",
                "items": "long"
              }
            },
            {
              "name": "routed_at",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            }
          ],
          "name": "RouteTransactionsEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "item_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "product_id",
              "type": "string"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "booking_room_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "amount",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "name": "posted",
              "type": "boolean"
            },
            {
              "name": "quantity",
              "type": "int"
            },
            {
              "name": "fees",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Fee"
              }
            },
            {
              "name": "taxes",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Tax"
              }
            },
            {
              "name": "notes",
              "default": null,
              "type": [
                "null",
                "string"
              ]
            },
            {
              "name": "folio_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            }
          ],
          "name": "ItemEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "manual_tax_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "tax_id",
              "type": "string"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "reservation_room_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "amount",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "name": "posted",
              "type": "boolean"
            },
            {
              "name": "quantity",
              "type": "int"
            },
            {
              "name": "notes",
              "default": null,
              "type": [
                "null",
                "string"
              ]
            },
            {
              "name": "folio_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            }
          ],
          "name": "ManualTaxEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "manual_fee_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "fee_id",
              "type": "string"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "reservation_room_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "amount",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "name": "posted",
              "type": "boolean"
            },
            {
              "name": "quantity",
              "type": "int"
            },
            {
              "name": "taxes",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Tax"
              }
            },
            {
              "name": "notes",
              "default": null,
              "type": [
                "null",
                "string"
              ]
            },
            {
              "name": "folio_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            }
          ],
          "name": "ManualFeeEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "custom_item_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "product_id",
              "type": "string"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "booking_room_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "amount",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "name": "posted",
              "type": "boolean"
            },
            {
              "name": "quantity",
              "type": "int"
            },
            {
              "name": "fees",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Fee"
              }
            },
            {
              "name": "taxes",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Tax"
              }
            },
            {
              "name": "notes",
              "default": null,
              "type": [
                "null",
                "string"
              ]
            },
            {
              "name": "folio_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            }
          ],
          "name": "CustomItemEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "room_revenue_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "room_type_id",
              "default": null,
              "type": [
                "null",
                "string"
              ]
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "reservation_room_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "amount",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "name": "posted",
              "type": "boolean"
            },
            {
              "name": "type",
              "type": {
                "name": "ReservationRevenueEventType",
                "type": "enum",
                "symbols": [
                  "MANUAL",
                  "NO_SHOW",
                  "CANCELLATION"
                ]
              }
            },
            {
              "name": "fees",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Fee"
              }
            },
            {
              "name": "taxes",
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Tax"
              }
            },
            {
              "name": "notes",
              "default": null,
              "type": [
                "null",
                "string"
              ]
            },
            {
              "name": "folio_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            }
          ],
          "name": "RoomRevenueEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "adjustment_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "id",
              "type": "string"
            },
            {
              "name": "reservation_room_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "description",
              "type": "string"
            },
            {
              "name": "transaction_datetime",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "default": null,
              "name": "customer_id",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "amount",
              "type": [
                "null",
                "long"
              ]
            },
            {
              "default": "USD",
              "name": "currency",
              "type": {
                "connect.default": "USD",
                "type": "string"
              }
            },
            {
              "name": "posted",
              "type": "boolean"
            },
            {
              "name": "quantity",
              "type": "int"
            },
            {
              "name": "type",
              "type": {
                "name": "AdjustmentType",
                "type": "enum",
                "symbols": [
                  "ITEM",
                  "TAX",
                  "FEE",
                  "ROOM_REVENUE",
                  "RATE"
                ]
              }
            },
            {
              "name": "fees",
              "default": [],
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Fee"
              }
            },
            {
              "name": "taxes",
              "default": [],
              "type": {
                "type": "array",
                "items": "com.cloudbeds.AccountingService.Tax"
              }
            },
            {
              "name": "adjusted_item_id", //product_id, tax_id, fee_id, room_type_id
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "revenue_type",
              "default": null,
              "type": [
                "null",
                "com.cloudbeds.AccountingService.ReservationRevenueEventType"
              ]
            },
            {
              "name": "notes",
              "default": null,
              "type": [
                "null",
                "string"
              ]
            },
            {
              "name": "folio_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            }
          ],
          "name": "AdjustmentEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "void_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "transaction_id",
              "default": null,
              "type": [
                "null", "long"
              ]
            },
            {
              "name": "external_relation",
              "default": null,
              "type": [
                "null",
                {
                  "type": "record",
                  "fields": [
                    {
                      "name": "id",
                      "type": "string"
                    },
                    {
                      "name": "type",
                      "type": {
                        "name": "ExternalRelationType",
                        "type": "enum",
                        "symbols": [
                          "ITEM",
                          "ITEM_POS",
                          "ADDON",
                          "ADJUSTMENT",
                          "TAX",
                          "FEE",
                          "ROOM_REVENUE"
                        ]
                      }
                    }
                  ],
                  "name": "ExternalRelation"
                }
              ]
            },
            {
              "name": "voided_at",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            }
          ],
          "name": "VoidEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "deleted_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "deleted_at",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "type",
              "type": {
                "name": "DeletedEventType",
                "type": "enum",
                "symbols": [
                  "RESERVATION",
                  "ROOM",
                  "ADDON",
                  "CUSTOM_ITEM"
                ]
              }
            },
            {
              "name": "ids",
              "type": {
                "type": "array",
                "items": "long"
              }
            }
          ],
          "name": "DeletedEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "reservation_status",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "id",
              "type": "long"
            },
            {
              "name": "old_status",
              "type": "com.cloudbeds.AccountingService.ReservationStatus"
            },
            {
              "name": "new_status",
              "type": "com.cloudbeds.AccountingService.ReservationStatus"
            },
            {
              "name": "updated_at",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "last_night_audit_date",
              "default": null,
              "type": [
                "null",
                {
                  "connect.name": "org.apache.kafka.connect.data.Date",
                  "connect.version": 1,
                  "logicalType": "date",
                  "type": "int"
                }
              ]
            }
          ],
          "name": "ReservationStatusEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "payment_event",
      "default": null,
      "type": [
        "null",
        {
          "type": "record",
          "name": "PaymentEvent",
          "fields": [
            {
              "name": "payment",
              "type": "com.cloudbeds.AccountingService.payments.Payment"
            },
            {
              "name": "payment_event_details",
              "type": "com.cloudbeds.AccountingService.payments.PaymentEventDetails"
            }
          ]
        }
      ]
    },
    {
      "name": "payment_captured_event",
      "default": null,
      "type": [
        "null",
        {
          "type": "record",
          "name": "PaymentCapturedEvent",
          "fields": [
            {
              "name": "event_timestamp",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "payment",
              "type": "com.cloudbeds.AccountingService.payments.Payment"
            },
            {
              "name": "status",
              "type": "com.cloudbeds.AccountingService.payments.PaymentStatus"
            },
            {
              "name": "amount",
              "type": "long"
            },
            {
              "name": "currency",
              "type": "string"
            },
            {
              "name": "capture_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            }
          ]
        }
      ]
    },
    {
      "name": "night_audit_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "night_audit_id",
              "type": "long"
            },
            {
              "name": "event_id",
              "default": null,
              "type": [
                "null",
                "long"
              ]
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "started_at",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "process_from",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "process_to",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "reservation_room_ids",
              "default": [],
              "type": {
                "type": "array",
                "items": "long"
              }
            },
            {
              "name": "transaction_ids",
              "default": [],
              "type": {
                "type": "array",
                "items": "long"
              }
            }
          ],
          "name": "NightAuditEvent",
          "type": "record"
        }
      ]
    },
    {
      "name": "refund_event",
      "default": null,
      "type": [
        "null",
        {
          "type": "record",
          "name": "RefundEvent",
          "fields": [
            {
              "name": "payment_event_details",
              "type": "com.cloudbeds.AccountingService.payments.PaymentEventDetails"
            },
            {
              "name": "refund",
              "type": "com.cloudbeds.AccountingService.payments.PaymentRefund"
            }
          ]
        }
      ]
    },
    {
      "name": "payment_voided_event",
      "default": null,
      "type": [
        "null",
        {
          "type": "record",
          "name": "PaymentVoidedEvent",
          "fields": [
            {
              "name": "id",
              "type": "long"
            },
            {
              "name": "created_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "payment",
              "type": "com.cloudbeds.AccountingService.payments.Payment"
            },
            {
              "name": "use_created_at_as_transaction_datetime",
              "type": "boolean",
              "default": false
            }
          ]
        }
      ]
    },
    {
      "name": "addon_posted_event",
      "default": null,
      "type": [
        "null",
        {
          "name": "AddonPostedEvent",
          "type": "record",
          "fields": [
            {
              "name": "reservation_addon_ids",
              "type": {
                "type": "array",
                "items": "long"
              }
            }
          ]
        }
      ]
    },
    {
      "name": "routed_pending_transaction_event",
      "default": null,
      "type": [
        "null",
        {
          "name": "RoutedPendingTransactionEvent",
          "type": "record",
          "fields": [
            {
              "name": "operation",
              "type": {
                "name": "RoutedPendingOperation",
                "type": "enum",
                "symbols": [
                  "ROUTE",
                  "UN_ROUTE"
                ]
              }
            },
            {
              "name": "routed_to_source_id",
              "type": "long"
            },
            {
              "name": "transaction_params",
              "type": {
                "type": "array",
                "items": {
                  "fields": [
                    {
                      "name": "service_date",
                      "type": {
                        "connect.name": "org.apache.kafka.connect.data.Date",
                        "connect.version": 1,
                        "logicalType": "date",
                        "type": "int"
                      }
                    },
                    {
                      "name": "reservation_room_id",
                      "type": "long"
                    },
                    {
                      "name": "details",
                      "default": null,
                      "type": [
                        "null",
                        {
                          "name": "RoutedPendingTransactionDetails",
                          "type": "record",
                          "fields": [
                            {
                              "name": "id",
                              "type": "long"
                            },
                            {
                              "name": "parent_id",
                              "default": null,
                              "type": [
                                "null",
                                "long"
                              ]
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "name": "transaction_type",
                      "type": {
                          "name": "RouteTransactionType",
                          "type": "enum",
                          "symbols": [
                            "RATE",
                            "FEE",
                            "TAX"
                          ]
                        }
                    },
                    {
                      "name": "relation_type",
                      "type": {
                        "name": "RoutedTransactionRelationType",
                        "type": "enum",
                        "symbols": [
                          "ROOM",
                          "RESERVATION"
                        ]
                      }
                    }
                  ],
                  "name": "RoutePendingTransactionParam",
                  "type": "record"
                }
              }
            }
          ]
        }
      ]
    },
    {
      "name": "payment_chargeback_event",
      "default": null,
      "type": [
        "null",
        {
          "type": "record",
          "name": "PaymentChargebackEvent",
          "fields": [
            {
              "name": "payment_event_details",
              "type": "com.cloudbeds.AccountingService.payments.PaymentEventDetails"
            },
            {
              "name": "chargeback",
              "type": "com.cloudbeds.AccountingService.payments.PaymentChargeback"
            }
          ]
        }
      ]
    },
    {
      "name": "payment_adjustment_event",
      "default": null,
      "type": [
        "null",
        {
          "type": "record",
          "name": "PaymentAdjustmentEvent",
          "fields": [
            {
              "name": "payment_event_details",
              "type": "com.cloudbeds.AccountingService.payments.PaymentEventDetails"
            },
            {
              "name": "adjustment",
              "type": "com.cloudbeds.AccountingService.payments.PaymentAdjustment"
            }
          ]
        }
      ]
    },
    {
      "name": "payment_chargeback_reversal_event",
      "default": null,
      "type": [
        "null",
        {
          "type": "record",
          "name": "PaymentChargebackReversalEvent",
          "fields": [
            {
              "name": "payment_event_details",
              "type": "com.cloudbeds.AccountingService.payments.PaymentEventDetails"
            },
            {
              "name": "chargeback_reversal",
              "type": "com.cloudbeds.AccountingService.payments.PaymentChargebackReversal"
            }
          ]
        }
      ]
    },
    {
      "name": "moved_pending_transaction_event",
      "default": null,
      "type": [
        "null",
        {
          "name": "MovedPendingTransactionEvent",
          "type": "record",
          "fields": [
            {
              "name": "operation",
              "type": {
                "name": "MovedPendingOperation",
                "type": "enum",
                "symbols": [
                  "MOVE",
                  "UNDO"
                ]
              }
            },
            {
              "name": "transaction_params",
              "type": {
                "type": "array",
                "items": {
                  "fields": [
                    {
                      "name": "folio_id",
                      "type": "long"
                    },
                    {
                      "name": "folio_source_id",
                      "type": "long"
                    },
                    {
                      "name": "folio_source_kind",
                      "type": "MfdTransactionSourceKind"
                    },
                    {
                      "name": "service_date",
                      "type": {
                        "connect.name": "org.apache.kafka.connect.data.Date",
                        "connect.version": 1,
                        "logicalType": "date",
                        "type": "int"
                      }
                    },
                    {
                      "name": "reservation_room_id",
                      "type": "long"
                    },
                    {
                      "name": "details",
                      "default": null,
                      "type": [
                        "null",
                        {
                          "name": "MovedPendingTransactionDetails",
                          "type": "record",
                          "fields": [
                            {
                              "name": "id",
                              "type": "long"
                            },
                            {
                              "name": "parent_id",
                              "default": null,
                              "type": [
                                "null",
                                "long"
                              ]
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "name": "transaction_type",
                      "type": {
                        "name": "MovedTransactionType",
                        "type": "enum",
                        "symbols": [
                          "RATE",
                          "FEE",
                          "TAX"
                        ]
                      }
                    },
                    {
                      "name": "relation_type",
                      "type": {
                        "name": "MovedTransactionRelationType",
                        "type": "enum",
                        "symbols": [
                          "ROOM",
                          "RESERVATION"
                        ]
                      }
                    }
                  ],
                  "name": "MovedPendingTransactionParam",
                  "type": "record"
                }
              }
            }
          ]
        }
      ]
    },
    {
      "name": "balance_transfer_accounts_receivable_event",
      "default": null,
      "type": [
        "null",
        {
          "name": "BalanceTransferAccountsReceivableEvent",
          "type": "record",
          "fields": [
            {
              "name": "event_id",
              "type": "long"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "created_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "accounts_receivable_ledger_id",
              "type": "long"
            }
          ]
        }
      ]
    },
    {
      "name": "balance_transfer_undo_accounts_receivable_event",
      "default": null,
      "type": [
        "null",
        {
          "name": "BalanceTransferUndoAccountsReceivableEvent",
          "type": "record",
          "fields": [
            {
              "name": "event_id",
              "type": "long"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "created_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "accounts_receivable_ledger_id",
              "type": "long"
            }
          ]
        }
      ]
    },
    {
      "name": "transfer_deposit_event",
      "default": null,
      "type": [
        "null",
        {
          "name": "TransferDepositEvent",
          "type": "record",
          "fields": [
            {
              "name": "event_id",
              "type": "long"
            },
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "created_at",
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "transaction_ids",
              "type": {
                "type": "array",
                "items": "long"
              }
            }
          ]
        }
      ]
    },
    {
      "name": "move_between_folios_event",
      "default": null,
      "type": [
        "null",
        {
          "fields": [
            {
              "name": "property_id",
              "type": "long"
            },
            {
              "name": "user_id",
              "type": "long"
            },
            {
              "name": "folio_id",
              "type": "long"
            },
            {
              "name": "transaction_ids",
              "type": {
                "type": "array",
                "items": "long"
              }
            },
            {
              "name": "moved_at",
              "default": 0,
              "type": {
                "connect.name": "org.apache.kafka.connect.data.Timestamp",
                "connect.version": 1,
                "logicalType": "timestamp-millis",
                "type": "long"
              }
            }
          ],
          "name": "MoveTransactionsBetweenFoliosEvent",
          "type": "record"
        }
      ]
    }
  ],
  "name": "TransactionEventValue",
  "namespace": "com.cloudbeds.AccountingService",
  "type": "record"
}
