{"fields": [{"name": "id", "type": "string"}, {"default": null, "name": "reservation_id", "type": ["null", "long"]}, {"default": null, "name": "property_id", "type": ["null", "long"]}, {"default": null, "name": "datetime_created", "type": ["null", {"logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "datetime_updated", "type": ["null", {"logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "datetime_post", "type": ["null", {"logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "datetime_transaction", "type": ["null", {"logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "user_id", "type": ["null", "long"]}, {"default": null, "name": "user_name", "type": ["null", "string"]}, {"default": null, "name": "room_id", "type": ["null", "string"]}, {"default": null, "name": "room_name", "type": ["null", "string"]}, {"default": null, "name": "reservation_name", "type": ["null", "string"]}, {"default": null, "name": "reservation_identifier", "type": ["null", "string"]}, {"default": null, "name": "res_room_identifier", "type": ["null", "string"]}, {"default": null, "name": "term_code", "type": ["null", "string"]}, {"default": null, "name": "description", "type": ["null", "string"]}, {"default": null, "name": "notes", "type": ["null", "string"]}, {"default": null, "name": "qty", "type": ["null", "int"]}, {"default": null, "name": "debit", "type": ["null", "double"]}, {"default": null, "name": "debit_money", "type": ["null", "double"]}, {"default": null, "name": "debit_scale", "type": ["null", "int"]}, {"default": null, "name": "credit", "type": ["null", "double"]}, {"default": null, "name": "credit_money", "type": ["null", "double"]}, {"default": null, "name": "credit_scale", "type": ["null", "int"]}, {"default": null, "name": "currency", "type": ["null", "string"]}, {"default": null, "name": "type", "type": ["null", "string"]}, {"default": null, "name": "reservation_room_id", "type": ["null", "long"]}, {"default": null, "name": "parent_id", "type": ["null", "string"]}, {"default": null, "name": "house_account_id", "type": ["null", "long"]}, {"default": null, "name": "group_profile_id", "type": ["null", "long"]}, {"default": null, "name": "allotment_block_id", "type": ["null", "long"]}, {"default": null, "name": "is_private_ha", "type": ["null", "int"]}, {"default": null, "name": "drawer_id", "type": ["null", "long"]}, {"default": null, "name": "drawer_action_id", "type": ["null", "long"]}, {"default": null, "name": "link_id", "type": ["null", "string"]}, {"default": null, "name": "source_type", "type": ["null", "string"]}, {"default": null, "name": "source_id", "type": ["null", "string"]}, {"default": null, "name": "description_filter", "type": ["null", "string"]}, {"default": null, "name": "posted", "type": ["null", "int"]}, {"default": null, "name": "deleted", "type": ["null", "int"]}, {"default": null, "name": "void_id", "type": ["null", "string"]}, {"default": null, "name": "void", "type": ["null", "int"]}, {"default": null, "name": "adjust_root_id", "type": ["null", "string"]}, {"default": null, "name": "is_refund", "type": ["null", "int"]}, {"default": null, "name": "customer_id", "type": ["null", "long"]}, {"default": null, "name": "state", "type": ["null", "string"]}, {"default": null, "name": "readonly", "type": ["null", "int"]}, {"default": null, "name": "internal_type_code", "type": ["null", "string"]}, {"default": null, "name": "internal_type_description", "type": ["null", "string"]}], "name": "AccountReceivableTransactionsValue", "namespace": "com.cloudbeds.AccountingService", "type": "record"}