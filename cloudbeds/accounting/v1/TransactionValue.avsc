{"type": "record", "name": "TransactionValue", "namespace": "com.cloudbeds.AccountingService", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": ["null", "long"]}, {"name": "source_datetime", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "transaction_datetime", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "transaction_datetime_property_time", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"default": null, "name": "service_date", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Date", "connect.version": 1, "logicalType": "date", "type": "int"}]}, {"name": "internal_code", "type": "string"}, {"name": "amount", "type": ["null", "long"]}, {"name": "currency", "type": ["null", "string"]}, {"name": "currency_scale", "default": null, "type": ["null", "int"]}, {"name": "customer_id", "type": ["null", "long"]}, {"name": "parent_id", "type": ["null", "long"]}, {"name": "source_id", "type": "long"}, {"name": "source_kind", "type": [{"type": "enum", "name": "SourceKindEnum", "symbols": ["RESERVATION", "HOUSE_ACCOUNT", "GROUP_PROFILE", "CITY_LEDGER", "ACCOUNTS_RECEIVABLE_LEDGER"]}]}, {"name": "external_relation_id", "type": ["null", "string"]}, {"name": "origin_id", "type": ["null", "string"]}, {"name": "routed_from", "type": ["null", "long"]}, {"name": "quantity", "type": ["null", "int"]}, {"name": "description", "type": ["null", "string"]}, {"name": "notes", "default": null, "type": ["null", "string"]}, {"name": "user_id", "type": ["null", "long"]}, {"name": "created_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "root_id", "type": ["null", "long"]}, {"name": "sub_source_id", "type": ["null", "long"]}, {"name": "external_relation_kind", "type": ["null", {"type": "enum", "name": "ExternalRelationKindEnum", "symbols": ["RESERVATION", "ROOM", "PAYMENT", "ITEM", "ITEM_POS", "ADDON", "CITY_LEDGER", "ACCOUNTS_RECEIVABLE", "ROOM_REVENUE", "FEE", "TAX", "ADJUSTMENT", "PAYMENT_FEE"]}]}, {"name": "account", "default": null, "type": ["null", "com.cloudbeds.AccountingService.TransactionAccount"]}]}