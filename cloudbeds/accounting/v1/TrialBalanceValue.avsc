{"type": "record", "name": "TrialBalanceValue", "namespace": "com.cloudbeds.AccountingService", "fields": [{"name": "property_id", "type": "long"}, {"name": "trial_balance_id", "type": "string"}, {"name": "business_date", "type": {"connect.name": "org.apache.kafka.connect.data.Date", "connect.version": 1, "logicalType": "date", "type": "int"}}, {"name": "night_audit_from_id", "type": "long"}, {"name": "night_audit_from_completed_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "night_audit_to_id", "type": "long"}, {"name": "night_audit_to_completed_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "deposits_ledger_opening_balance", "type": "long"}, {"name": "accounts_receivable_ledger_opening_balance", "type": "long"}, {"name": "guest_ledger_opening_balance", "type": "long"}, {"name": "created_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "updated_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}]}