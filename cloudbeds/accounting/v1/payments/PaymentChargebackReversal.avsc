{"type": "record", "name": "PaymentChargebackReversal", "namespace": "com.cloudbeds.AccountingService.payments", "fields": [{"name": "id", "type": "long"}, {"name": "amount", "type": "long"}, {"name": "currency", "type": "string"}, {"name": "status", "type": "com.cloudbeds.AccountingService.payments.PaymentStatus"}, {"name": "created_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "payment", "type": "com.cloudbeds.AccountingService.payments.Payment"}]}