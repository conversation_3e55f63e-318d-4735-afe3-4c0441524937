{"type": "record", "name": "PaymentRefund", "namespace": "com.cloudbeds.AccountingService.payments", "fields": [{"name": "id", "type": "long"}, {"name": "sub_source_id", "type": ["null", "long"]}, {"name": "created_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "processed_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "amount", "type": "long"}, {"name": "currency", "type": "string"}, {"name": "note", "type": "string"}, {"name": "status", "type": "com.cloudbeds.AccountingService.payments.PaymentStatus"}, {"name": "is_payment_deposit", "type": "boolean", "default": false}, {"name": "payment_id", "type": ["null", "long"], "default": null}, {"name": "payment_method", "type": "com.cloudbeds.AccountingService.payments.PaymentMethod"}, {"name": "processing_details", "type": "com.cloudbeds.AccountingService.payments.PaymentProcessingDetails"}, {"name": "folio_id", "default": null, "type": ["null", "long"]}]}