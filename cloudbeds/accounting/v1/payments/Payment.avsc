{"type": "record", "name": "Payment", "namespace": "com.cloudbeds.AccountingService.payments", "fields": [{"name": "id", "type": "long"}, {"name": "created_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "processed_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "property_id", "type": "long"}, {"name": "sub_source_id", "type": ["null", "long"]}, {"name": "user_id", "type": "long"}, {"name": "is_deposit", "type": "boolean", "default": false}, {"name": "amount", "type": "long"}, {"name": "currency", "type": "string"}, {"name": "status", "type": "com.cloudbeds.AccountingService.payments.PaymentStatus"}, {"name": "capture_mode", "type": "com.cloudbeds.AccountingService.payments.PaymentCaptureMode"}, {"name": "note", "type": ["null", "string"]}, {"name": "capture_details", "type": {"type": "array", "items": {"fields": [{"name": "id", "type": "long"}, {"name": "amount", "type": "long"}, {"name": "currency", "type": "string"}], "type": "record", "name": "PaymentCaptureSummary"}}}, {"name": "refund_details", "type": {"type": "array", "items": {"fields": [{"name": "id", "type": "long"}, {"name": "amount", "type": "long"}, {"name": "currency", "type": "string"}], "type": "record", "name": "PaymentRefundSummary"}}}, {"name": "payment_method", "type": "com.cloudbeds.AccountingService.payments.PaymentMethod"}, {"name": "processing_details", "type": "com.cloudbeds.AccountingService.payments.PaymentProcessingDetails"}, {"name": "adjustment_details", "type": {"type": "array", "items": {"type": "record", "name": "PaymentAdjustmentSummary", "fields": [{"name": "id", "type": "long"}, {"name": "amount", "type": "long"}, {"name": "currency", "type": "string"}]}}}, {"name": "convenience_fee", "default": null, "type": ["null", {"type": "record", "name": "PaymentConvenienceFee", "fields": [{"name": "id", "type": "long"}, {"name": "amount", "type": "long"}, {"name": "currency", "type": "string"}, {"name": "name", "type": "string"}]}]}, {"name": "folio_id", "default": null, "type": ["null", "long"]}]}