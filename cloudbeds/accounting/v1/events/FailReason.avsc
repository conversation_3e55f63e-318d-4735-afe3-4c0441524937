{"namespace": "com.cloudbeds.AccountingService.events", "name": "FailReason", "type": "record", "fields": [{"name": "code", "type": {"name": "ErrorCode", "type": "enum", "symbols": ["UNEXPECTED_ERROR", "BOOKING_NOT_FOUND", "ACCOUNTS_RECEIVABLE_LEDGER_NOT_FOUND", "ACCOUNTS_RECEIVABLE_LEDGER_STATUS_ERROR", "BOOKING_STATUS_ERROR", "ACCOUNTS_RECEIVABLE_TRANSACTION_EXIST_ERROR", "ACCOUNTS_RECEIVABLE_TRANSACTION_NOT_EXIST_ERROR", "BALANCE_LESS_OR_EQUALS_TO_ZERO_ERROR", "DEPOSIT_TRANSACTION_CAN_NOT_BE_TRANSFERRED"]}}, {"name": "message", "type": "string"}]}