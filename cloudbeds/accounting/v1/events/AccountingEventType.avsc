{"namespace": "com.cloudbeds.AccountingService.events", "name": "AccountingEventType", "type": "enum", "symbols": ["TRANSFER_DEPOSIT_START", "TRANSFER_DEPOSIT_SUCCESSFUL", "TRANSFER_DEPOSIT_FAILED", "BALANCE_TRANSFER_ACCOUNTS_RECEIVABLE_LEDGER_START", "BALANCE_TRANSFER_ACCOUNTS_RECEIVABLE_LEDGER_SUCCESSFUL", "BALANCE_TRANSFER_ACCOUNTS_RECEIVABLE_LEDGER_FAILED", "BALANCE_TRANSFER_UNDO_ACCOUNTS_RECEIVABLE_LEDGER_START", "BALANCE_TRANSFER_UNDO_ACCOUNTS_RECEIVABLE_LEDGER_SUCCESSFUL", "BALANCE_TRANSFER_UNDO_ACCOUNTS_RECEIVABLE_LEDGER_FAILED", "RESERVATION_CHECK_IN_SUCCESSFUL", "RESERVATION_CHECK_OUT_SUCCESSFUL"]}