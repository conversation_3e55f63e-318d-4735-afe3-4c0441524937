{"fields": [{"name": "id", "type": "long"}, {"name": "source_id", "type": "long"}, {"name": "source_kind", "type": "com.cloudbeds.AccountingService.SourceKindEnum"}, {"name": "property_id", "type": "long"}, {"name": "user_id", "type": "long"}, {"name": "type", "type": "com.cloudbeds.AccountingService.events.AccountingEventType"}, {"name": "event_timestamp", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "balance_transfer_accounts_receivable_ledger_start_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.BalanceTransferAccountsReceivableLedgerStartEvent"]}, {"name": "balance_transfer_accounts_receivable_ledger_successful_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.BalanceTransferAccountsReceivableLedgerSuccessfulEvent"]}, {"name": "balance_transfer_accounts_receivable_ledger_failed_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.BalanceTransferAccountsReceivableLedgerFailedEvent"]}, {"name": "balance_transfer_undo_accounts_receivable_ledger_start_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.BalanceTransferUndoAccountsReceivableLedgerStartEvent"]}, {"name": "balance_transfer_undo_accounts_receivable_ledger_successful_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.BalanceTransferUndoAccountsReceivableLedgerSuccessfulEvent"]}, {"name": "balance_transfer_undo_accounts_receivable_ledger_failed_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.BalanceTransferUndoAccountsReceivableLedgerFailedEvent"]}, {"name": "transfer_deposit_start_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.TransferDepositStartEvent"]}, {"name": "transfer_deposit_successful_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.TransferDepositSuccessfulEvent"]}, {"name": "transfer_deposit_failed_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.TransferDepositFailedEvent"]}, {"name": "reservation_check_in_successful_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.ReservationCheckInSuccessfulEvent"]}, {"name": "reservation_check_out_successful_event", "default": null, "type": ["null", "com.cloudbeds.AccountingService.events.ReservationCheckOutSuccessfulEvent"]}], "name": "AccountingEventValue", "namespace": "com.cloudbeds.AccountingService.events", "type": "record"}