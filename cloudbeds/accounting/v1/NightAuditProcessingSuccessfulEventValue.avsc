{"name": "NightAuditProcessingSuccessfulEvent", "namespace": "com.cloudbeds.AccountingService", "type": "record", "fields": [{"name": "property_id", "type": "long"}, {"name": "night_audit_id", "type": "long"}, {"name": "night_audit_period_start", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "night_audit_period_end", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "completed_at", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}]}