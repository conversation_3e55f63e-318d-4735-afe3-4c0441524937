{"name": "TransactionAccount", "namespace": "com.cloudbeds.AccountingService", "type": "record", "fields": [{"name": "id", "type": "long"}, {"name": "description", "type": "string"}, {"name": "name", "type": "string"}, {"name": "property_id", "type": "long"}, {"name": "chart_of_account_type", "type": {"type": "enum", "name": "ChartOfAccountType", "symbols": ["LIABILITIES", "REVENUE", "ASSETS", "EQUITY", "EXPENSES"]}}, {"name": "category", "type": {"type": "enum", "name": "AccountCategory", "symbols": ["DEPOSITS"]}}, {"name": "created_by", "type": "long"}]}