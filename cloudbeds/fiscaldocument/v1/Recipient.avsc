{"type": "record", "name": "Recipient", "namespace": "com.cloudbeds.FiscalDocumentService", "fields": [{"name": "id", "type": ["null", "string"], "default": null}, {"name": "first_name", "type": ["null", "string"], "default": null}, {"name": "last_name", "type": ["null", "string"], "default": null}, {"name": "email", "type": ["null", "string"], "default": null}, {"name": "type", "type": {"type": "enum", "name": "RecipientType", "symbols": ["COMPANY", "PERSON"]}}, {"name": "address", "type": ["null", {"type": "record", "name": "Address", "fields": [{"name": "address_1", "type": ["null", "string"], "default": null}, {"name": "address_2", "type": ["null", "string"], "default": null}, {"name": "city", "type": ["null", "string"], "default": null}, {"name": "state", "type": ["null", "string"], "default": null}, {"name": "zip_code", "type": ["null", "string"], "default": null}, {"name": "country", "type": ["null", "string"], "default": null}]}], "default": null}, {"name": "tax", "type": ["null", {"type": "record", "name": "TaxInfo", "fields": [{"name": "id", "type": ["null", "string"], "default": null}, {"name": "company_name", "type": ["null", "string"], "default": null}]}], "default": null}, {"name": "contact_details", "type": ["null", {"type": "record", "name": "ContactDetails", "fields": [{"name": "phone", "type": ["null", "string"], "default": null}, {"name": "gender", "type": ["null", "string"], "default": null}, {"name": "cell_phone", "type": ["null", "string"], "default": null}, {"name": "birthday", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}]}], "default": null}, {"name": "document", "type": ["null", {"type": "record", "name": "Document", "fields": [{"name": "type", "type": ["null", "string"], "default": null}, {"name": "number", "type": ["null", "string"], "default": null}, {"name": "issuing_country", "type": ["null", "string"], "default": null}, {"name": "issue_date", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}, {"name": "expiration_date", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}]}], "default": null}, {"name": "country_data", "type": {"type": "map", "values": ["null", "string", "int", "long", "float", "double", "boolean"]}, "default": {}}]}