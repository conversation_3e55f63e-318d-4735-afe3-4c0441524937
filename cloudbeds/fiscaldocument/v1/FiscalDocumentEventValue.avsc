{"fields": [{"name": "source_id", "type": "long"}, {"name": "property_id", "default": 0, "type": "long"}, {"name": "source_kind", "type": "com.cloudbeds.FiscalDocumentService.SourceKind"}, {"name": "type", "type": {"name": "FiscalDocumentEventType", "type": "enum", "symbols": ["MFD_INVOICE_EVENT", "INVOICE_CREATE_EVENT", "CREDIT_NOTE_CREATE_EVENT", "RECTIFY_INVOICE_CREATE_EVENT", "INTEGRATION_CREATE_EVENT", "CREATE_DOCUMENT_EVENT"]}}, {"name": "mfd_invoice_event", "default": null, "type": ["null", {"fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "group_profile_id", "type": ["null", "long"], "default": null}, {"name": "reservation_id", "type": "long"}, {"name": "invoice_setup_id", "type": ["null", "long"], "default": null}, {"name": "invoice_number", "type": ["null", "long"], "default": null}, {"name": "type", "type": {"type": "string", "connect.default": "reservation"}, "default": "reservation"}, {"name": "transactions", "type": ["null", "string"], "default": null}, {"name": "recipients", "type": ["null", "string"], "default": null}, {"name": "generate_date", "type": {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}}, {"name": "pdf_url", "type": ["null", "string"], "default": null}, {"name": "email", "type": ["null", "string"], "default": null}, {"name": "sent", "type": "boolean"}, {"name": "credit_note_date", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}], "default": null}, {"name": "credit_note_url", "type": ["null", "string"], "default": null}, {"name": "credit_note_reason", "type": ["null", "string"], "default": null}, {"name": "credit_note_number", "type": ["null", "long"], "default": null}, {"name": "credit_note_setup_id", "type": ["null", "long"], "default": null}, {"name": "status", "type": [{"type": "string", "connect.version": 1, "connect.parameters": {"allowed": "open,paid,voided,requested,void_requested,failed,manually_reconciled,canceled,rejected"}, "connect.default": "open", "connect.name": "io.debezium.data.Enum"}, "null"], "default": "open"}, {"name": "user_id", "type": ["null", "long"], "default": null}, {"name": "amount", "type": ["null", "long"], "default": null}, {"name": "balance", "type": ["null", "long"], "default": null}, {"name": "currency", "type": ["null", "string"], "default": null}, {"name": "external_settings_id", "default": null, "type": ["null", "long"]}], "name": "MfdInvoiceEventValue", "type": "record"}]}, {"name": "invoice_create_event", "default": null, "type": ["null", {"fields": [{"name": "id", "type": "long"}, {"name": "transaction_ids", "type": {"type": "array", "items": "long"}}, {"name": "sequence_id", "default": null, "type": ["null", "long"]}, {"name": "user_id", "default": null, "type": ["null", "long"]}], "name": "InvoiceCreateEventValue", "type": "record"}]}, {"name": "credit_note_create_event", "default": null, "type": ["null", {"fields": [{"name": "id", "type": "long"}, {"name": "sequence_id", "default": null, "type": ["null", "long"]}, {"name": "transaction_ids", "type": ["null", {"type": "array", "items": "long"}], "default": null}, {"name": "method", "type": {"name": "CreditNoteMethod", "type": "enum", "symbols": ["ADJUSTMENT", "VOID"]}, "default": "VOID"}, {"name": "invoice_id", "type": "long"}, {"name": "reason", "type": ["null", "string"]}, {"name": "user_id", "default": null, "type": ["null", "long"]}], "name": "CreditNoteCreateEventValue", "type": "record"}]}, {"name": "rectify_invoice_create_event", "default": null, "type": ["null", {"fields": [{"name": "id", "type": "long"}, {"name": "transaction_ids", "type": ["null", {"type": "array", "items": "long"}], "default": null}, {"name": "method", "type": {"name": "RectifyCreateMethod", "type": "enum", "symbols": ["ADJUSTMENT", "VOID"]}}, {"name": "invoice_id", "type": "long"}, {"name": "reason", "type": ["null", "string"]}], "name": "RectifyInvoiceCreateEventValue", "type": "record"}]}, {"name": "create_document_event", "default": null, "type": ["null", {"fields": [{"name": "source_id", "type": "long"}, {"name": "source_kind", "type": "com.cloudbeds.FiscalDocumentService.SourceKind"}, {"name": "property_id", "type": "long"}, {"name": "kind", "type": "com.cloudbeds.FiscalDocumentService.FiscalDocumentKind"}, {"name": "document_trigger_event", "type": {"name": "DocumentTriggerEvent", "type": "enum", "symbols": ["RESERVATION_CHECK_OUT", "RESERVATION_CREATION"]}}], "name": "CreateDocumentEventValue", "type": "record"}]}, {"name": "integration_create_event", "default": null, "type": ["null", {"fields": [{"name": "id", "type": "long"}, {"name": "number", "type": ["null", "string"], "default": null}, {"name": "series", "type": ["null", "string"], "default": null}, {"name": "url", "type": ["null", "string"], "default": null}, {"name": "officialId", "type": ["null", "string"], "default": null}, {"name": "externalId", "type": ["null", "string"], "default": null}, {"name": "rectifyingInvoiceType", "type": ["null", "string"], "default": null}, {"name": "status", "type": ["null", "string"], "default": null}, {"name": "qrUrl", "type": ["null", "string"], "default": null}, {"name": "qrString", "type": ["null", "string"], "default": null}], "name": "IntegrationCreateEventValue", "type": "record"}]}], "name": "FiscalDocumentEventValue", "namespace": "com.cloudbeds.FiscalDocumentService", "type": "record"}