{"fields": [{"name": "id", "type": "long"}, {"name": "booking_id", "type": "long"}, {"name": "room_identifier", "type": "string"}, {"default": 1, "name": "room_group", "type": {"connect.default": 1, "type": "int"}}, {"default": -1, "name": "in_house", "type": {"connect.default": -1, "connect.type": "int16", "type": "int"}}, {"default": null, "name": "parent_id", "type": ["null", "long"]}, {"default": null, "name": "room_id", "type": ["null", "string"]}, {"default": null, "name": "event_id", "type": ["null", "long"]}, {"name": "adults", "type": "int"}, {"name": "kids", "type": "int"}, {"name": "start_date", "type": {"connect.name": "org.apache.kafka.connect.data.Date", "connect.version": 1, "logicalType": "date", "type": "int"}}, {"name": "end_date", "type": {"connect.name": "org.apache.kafka.connect.data.Date", "connect.version": 1, "logicalType": "date", "type": "int"}}, {"name": "room_type_id", "type": "long"}, {"name": "room_total", "type": "double"}, {"default": null, "name": "room_type_name", "type": ["null", "string"]}, {"name": "min_room_rate", "type": "double"}, {"name": "max_room_rate", "type": "double"}, {"name": "total", "type": "double"}, {"name": "detailed_rates", "type": "string"}, {"default": 1, "name": "adults_inBasePrice", "type": {"connect.default": 1, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "children_inBasePrice", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"name": "charge_additional_adult", "type": "string"}, {"name": "charge_additional_child", "type": "string"}, {"default": 0, "name": "add_adult_rate_first_night", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "add_adult_total", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "add_kid_rate_first_night", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "add_kids_total", "type": {"connect.default": 0, "type": "double"}}, {"name": "package", "type": "long"}, {"default": 0, "name": "guest_id", "type": {"connect.default": 0, "type": "long"}}, {"default": null, "name": "guest_first_name", "type": ["null", "string"]}, {"default": null, "name": "guest_last_name", "type": ["null", "string"]}, {"default": null, "name": "rate_id", "type": ["null", "long"]}, {"default": null, "name": "is_room_locked", "type": ["null", {"connect.type": "int16", "type": "int"}]}, {"default": "1970-01-01T00:00:00Z", "name": "created_at", "type": {"connect.default": "1970-01-01T00:00:00Z", "connect.name": "io.debezium.time.ZonedTimestamp", "connect.version": 1, "type": "string"}}, {"default": "1970-01-01T00:00:00Z", "name": "updated_at", "type": {"connect.default": "1970-01-01T00:00:00Z", "connect.name": "io.debezium.time.ZonedTimestamp", "connect.version": 1, "type": "string"}}, {"default": null, "name": "is_breakfast_included", "type": ["null", {"connect.type": "int16", "type": "int"}]}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}, {"default": null, "name": "__island", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_booking_rooms", "type": "record"}