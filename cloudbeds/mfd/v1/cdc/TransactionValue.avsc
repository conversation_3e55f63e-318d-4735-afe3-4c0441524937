{"connect.name": "com.cloudbeds.mfd.cdc.a_htl_financial_transactions.Value", "fields": [{"name": "id", "type": "string"}, {"name": "property_id", "type": "long"}, {"name": "datetime_created", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"default": 0, "name": "datetime_updated", "type": {"connect.default": 0, "connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "datetime_post", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "datetime_transaction", "type": {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"name": "user_id", "type": "long"}, {"name": "user_name", "type": "string"}, {"name": "room_id", "type": "string"}, {"name": "room_name", "type": "string"}, {"name": "reservation_name", "type": "string"}, {"name": "reservation_identifier", "type": "string"}, {"default": "0", "name": "res_room_identifier", "type": {"connect.default": "0", "type": "string"}}, {"default": null, "name": "term_code", "type": ["null", "string"]}, {"name": "description", "type": "string"}, {"name": "notes", "type": "string"}, {"default": 1, "name": "qty", "type": {"connect.default": 1, "type": "int"}}, {"default": 0, "name": "debit", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "debit_money", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "debit_scale", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "debit_raw", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "credit", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "credit_money", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "credit_scale", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "credit_raw", "type": {"connect.default": 0, "type": "double"}}, {"default": "$", "name": "currency", "type": {"connect.default": "$", "type": "string"}}, {"default": 0, "name": "posted", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "void", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"name": "type", "type": "string"}, {"default": 0, "name": "deleted", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": null, "name": "reservation_room_id", "type": ["null", "long"]}, {"default": null, "name": "parent_id", "type": ["null", "string"]}, {"name": "reservation_id", "type": "long"}, {"default": null, "name": "link_id", "type": ["null", "string"]}, {"default": null, "name": "source_type", "type": ["null", "string"]}, {"default": null, "name": "source_id", "type": ["null", "string"]}, {"default": null, "name": "description_filter", "type": ["null", "string"]}, {"default": null, "name": "void_id", "type": ["null", "string"]}, {"default": null, "name": "adjust_root_id", "type": ["null", "string"]}, {"default": null, "name": "house_account_id", "type": ["null", "long"]}, {"default": null, "name": "group_profile_id", "type": ["null", "long"]}, {"default": null, "name": "allotment_block_id", "type": ["null", "long"]}, {"default": null, "name": "is_private_ha", "type": ["null", {"connect.type": "int16", "type": "int"}]}, {"default": null, "name": "drawer_id", "type": ["null", "long"]}, {"default": null, "name": "drawer_action_id", "type": ["null", "long"]}, {"default": null, "name": "customer_id", "type": ["null", "long"]}, {"default": 0, "name": "is_refund", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": "completed", "name": "state", "type": {"connect.default": "completed", "type": "string"}}, {"default": 0, "name": "readonly", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_financial_transactions", "type": "record"}