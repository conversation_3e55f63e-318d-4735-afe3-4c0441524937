{"connect.name": "com.cloudbeds.mfd.cdc.a_htl_folios.Value", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"default": "", "name": "folio_name", "type": {"connect.default": "", "type": "string"}}, {"default": "", "name": "room_identifier", "type": {"connect.default": "", "type": "string"}}, {"default": null, "name": "parent_folio_id", "type": ["null", "long"]}, {"name": "link_id", "type": "long"}, {"default": "config", "name": "link_type", "type": {"connect.default": "config", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "config,reservation,house_account,group_account,allotment_block"}, "connect.version": 1, "type": "string"}}, {"default": 0, "name": "is_deleted", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "created_at", "type": {"connect.default": 0, "connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}}, {"default": null, "name": "updated_at", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}, {"default": null, "name": "__island", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_folios", "type": "record"}