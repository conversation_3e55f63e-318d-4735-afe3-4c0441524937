{"fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "product_id", "type": "long"}, {"name": "addon_name", "type": "string"}, {"default": null, "name": "addon_description", "type": ["null", "string"]}, {"default": "n/a", "name": "charge_type", "type": {"connect.default": "n/a", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "n/a,per_guest_per_night,per_guest,per_accommodation_per_night,per_accommodation,per_night,quantity,per_reservation"}, "connect.version": 1, "type": "string"}}, {"default": 0, "name": "charge_for_children", "type": [{"connect.default": 0, "connect.type": "int16", "type": "int"}, "null"]}, {"default": 0, "name": "charge_different_price_for_children", "type": [{"connect.default": 0, "connect.type": "int16", "type": "int"}, "null"]}, {"default": 0, "name": "max_qty_per_res", "type": [{"connect.default": 0, "type": "long"}, "null"]}, {"default": "n/a", "name": "available", "type": {"connect.default": "n/a", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "arrival_and_departure_dates,departure_date,arrival_date,n/a"}, "connect.version": 1, "type": "string"}}, {"default": "", "name": "transaction_code", "type": {"connect.default": "", "type": "string"}}, {"default": 0, "name": "addon_order", "type": {"connect.default": 0, "type": "long"}}, {"default": null, "name": "addon_photos", "type": ["null", "string"]}, {"default": 1, "name": "is_active", "type": {"connect.default": 1, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "is_deleted", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "post_when_checkin", "type": [{"connect.default": 0, "connect.type": "int16", "type": "int"}, "null"]}, {"default": 0, "name": "void_transaction", "type": [{"connect.default": 0, "connect.type": "int16", "type": "int"}, "null"]}, {"default": "booking_creation", "name": "posting_strategy", "type": {"connect.default": "booking_creation", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "checkin,booking_creation,daily"}, "connect.version": 1, "type": "string"}}, {"default": 0, "name": "is_fixed_price", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_addons", "type": "record"}