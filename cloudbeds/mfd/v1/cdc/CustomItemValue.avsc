{"connect.name": "com.cloudbeds.mfd.cdc.a_htl_custom_items.Value", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "source_id", "type": "long"}, {"name": "app_item_id", "type": "string"}, {"default": null, "name": "sku", "type": ["null", "string"]}, {"default": null, "name": "category_name", "type": ["null", "string"]}, {"name": "name", "type": "string"}, {"default": null, "name": "description", "type": ["null", "string"]}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_custom_items", "type": "record"}