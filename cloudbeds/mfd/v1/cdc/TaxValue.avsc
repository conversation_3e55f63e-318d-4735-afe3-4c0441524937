{"name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_taxes", "type": "record", "connect.name": "com.cloudbeds.mfd.cdc.a_htl_taxes.Value", "fields": [{"name": "id", "type": "long"}, {"default": null, "name": "child_id", "type": ["null", "long"]}, {"name": "property_id", "type": "long"}, {"default": null, "name": "code", "type": ["null", "string"]}, {"default": 1, "name": "is_active", "type": {"connect.default": 1, "connect.type": "int16", "type": "int"}}, {"default": null, "name": "set_on", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "expired_on", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "name_langs", "type": ["null", "string"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}]}