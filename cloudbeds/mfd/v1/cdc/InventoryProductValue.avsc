{"connect.name": "com.cloudbeds.mfd.cdc.a_htl_inventory_products.Value", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"default": null, "name": "category_id", "type": ["null", "long"]}, {"default": "", "name": "sku", "type": {"connect.default": "", "type": "string"}}, {"name": "product_name", "type": "string"}, {"default": "", "name": "product_code", "type": {"connect.default": "", "type": "string"}}, {"default": null, "name": "product_description", "type": ["null", "string"]}, {"default": 0, "name": "product_price", "type": {"connect.default": 0, "type": "double"}}, {"default": 1, "name": "is_active", "type": {"connect.default": 1, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "is_required_description", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "product_order", "type": {"connect.default": 0, "type": "int"}}, {"default": "product", "name": "product_type", "type": [{"connect.default": "product", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "product,service"}, "connect.version": 1, "type": "string"}, "null"]}, {"default": 1, "name": "do_not_track", "type": [{"connect.default": 1, "connect.type": "int16", "type": "int"}, "null"]}, {"default": 0, "name": "current_stock", "type": {"connect.default": 0, "type": "int"}}, {"default": 0, "name": "reorder_stock", "type": {"connect.default": 0, "type": "int"}}, {"default": null, "name": "stop_selling", "type": ["null", "int"]}, {"default": 0, "name": "send_notification", "type": [{"connect.default": 0, "connect.type": "int16", "type": "int"}, "null"]}, {"default": null, "name": "last_stock_update", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_inventory_products", "type": "record"}