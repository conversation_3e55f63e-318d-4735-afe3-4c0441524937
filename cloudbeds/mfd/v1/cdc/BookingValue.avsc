{"connect.name": "com.cloudbeds.mfd.cdc.a_htl_bookings.Value", "fields": [{"name": "id", "type": "long"}, {"name": "num_rooms", "type": "int"}, {"name": "property_id", "type": "long"}, {"name": "booking_type", "type": "string"}, {"default": "web", "name": "booking_via", "type": {"connect.default": "web", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "web,facebook,api,dashboard,third-party,myallocator,import"}, "connect.version": 1, "type": "string"}}, {"name": "customer_id", "type": "long"}, {"name": "customer_name", "type": "string"}, {"default": null, "name": "group_profile_id", "type": ["null", "long"]}, {"default": null, "name": "group_profile_name", "type": ["null", "string"]}, {"default": null, "name": "allotment_block_id", "type": ["null", "long"]}, {"default": null, "name": "allotment_block_name", "type": ["null", "string"]}, {"default": null, "name": "folio_config_id", "type": ["null", "long"]}, {"name": "first_name", "type": "string"}, {"name": "last_name", "type": "string"}, {"default": null, "name": "first_time", "type": ["null", {"connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "Y,N"}, "connect.version": 1, "type": "string"}]}, {"default": null, "name": "special_requests", "type": ["null", "string"]}, {"default": null, "name": "identifier", "type": ["null", "string"]}, {"default": null, "name": "third_party_identifier", "type": ["null", "string"]}, {"default": null, "name": "booking_date", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "checkin_date", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Date", "connect.version": 1, "logicalType": "date", "type": "int"}]}, {"default": null, "name": "checkout_date", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Date", "connect.version": 1, "logicalType": "date", "type": "int"}]}, {"default": "confirmed", "name": "status", "type": {"connect.default": "confirmed", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "in_progress,call2confirm,confirmed,canceled,checked_in,checked_out,not_confirmed,no_show,paypal_init,pending_payment"}, "connect.version": 1, "type": "string"}}, {"default": 0, "name": "total", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "grand_total", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "room_rate_total_adjustment", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "room_revenue_total_adjustment", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "room_revenue_total", "type": {"connect.default": 0, "type": "double"}}, {"default": "none", "name": "discount_type", "type": {"connect.default": "none", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "none,percentage,flat"}, "connect.version": 1, "type": "string"}}, {"default": null, "name": "discount_value", "type": ["null", "double"]}, {"default": 0, "name": "is_custom", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "taxes_value", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "taxes_sub_value", "type": {"connect.default": 0, "type": "double"}}, {"name": "policy_id", "type": "long"}, {"default": 0, "name": "third_party", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"name": "source", "type": "long"}, {"default": 0, "name": "is_root_source", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "ota_source_id", "type": [{"connect.default": 0, "type": "long"}, "null"]}, {"default": null, "name": "parent_source", "type": ["null", "long"]}, {"default": "", "name": "source_code", "type": {"connect.default": "", "type": "string"}}, {"default": 1, "name": "is_hotel_collect_booking", "type": {"connect.default": 1, "connect.type": "int16", "type": "int"}}, {"name": "association_id", "type": "long"}, {"default": 0, "name": "source_commission", "type": {"connect.default": 0, "type": "double"}}, {"default": null, "name": "association_fee_value", "type": ["null", "double"]}, {"default": null, "name": "association_fee_type", "type": ["null", {"connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "commission,per_night,per_room"}, "connect.version": 1, "type": "string"}]}, {"default": 0, "name": "source_commission_value", "type": {"connect.default": 0, "type": "double"}}, {"default": null, "name": "cancellation_numb", "type": ["null", "string"]}, {"default": null, "name": "cancellation_date", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": 0, "name": "additional_charges", "type": {"connect.default": 0, "type": "double"}}, {"name": "payment", "type": "string"}, {"default": null, "name": "credit_cards", "type": ["null", "string"]}, {"default": 0, "name": "paid_value", "type": {"connect.default": 0, "type": "double"}}, {"name": "booking_deposit", "type": "double"}, {"default": null, "name": "hear_about", "type": ["null", "string"]}, {"default": 3, "name": "commission_percentage", "type": {"connect.default": 3, "type": "double"}}, {"default": null, "name": "last_change", "type": ["null", {"connect.name": "io.debezium.time.ZonedTimestamp", "connect.version": 1, "type": "string"}]}, {"default": 0, "name": "delete", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": null, "name": "auto_change_date", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "additional_guests", "type": ["null", "string"]}, {"name": "prolongation_day", "type": "string"}, {"name": "document_number", "type": "string"}, {"name": "taxes_ids", "type": "string"}, {"name": "fees_ids", "type": "string"}, {"name": "currency_from", "type": "string"}, {"name": "currency_to", "type": "string"}, {"name": "currency_rate", "type": "double"}, {"default": null, "name": "imported_at", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Timestamp", "connect.version": 1, "logicalType": "timestamp-millis", "type": "long"}]}, {"default": null, "name": "booking_estimated_arrival_time", "type": ["null", "string"]}, {"default": 0, "name": "is_demo", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": 0, "name": "adults_number", "type": [{"connect.default": 0, "type": "int"}, "null"]}, {"default": 0, "name": "kids_number", "type": [{"connect.default": 0, "type": "int"}, "null"]}, {"default": null, "name": "lang", "type": ["null", "string"]}, {"default": 0, "name": "revenue", "type": {"connect.default": 0, "type": "double"}}, {"default": 0, "name": "rounding_method", "type": {"connect.default": 0, "connect.type": "int16", "type": "int"}}, {"default": "1970-01-01T00:00:00Z", "name": "created_at", "type": {"connect.default": "1970-01-01T00:00:00Z", "connect.name": "io.debezium.time.ZonedTimestamp", "connect.version": 1, "type": "string"}}, {"default": "1970-01-01T00:00:00Z", "name": "updated_at", "type": {"connect.default": "1970-01-01T00:00:00Z", "connect.name": "io.debezium.time.ZonedTimestamp", "connect.version": 1, "type": "string"}}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}, {"default": null, "name": "__island", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_bookings", "type": "record"}