{"connect.name": "com.cloudbeds.mfd.cdc.a_htl_folios_transactions.Value", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "transaction_id", "type": "string"}, {"default": "move", "name": "move_type", "type": {"connect.default": "move", "connect.name": "io.debezium.data.Enum", "connect.parameters": {"allowed": "move,route"}, "connect.version": 1, "type": "string"}}, {"name": "folio_id", "type": "long"}, {"default": null, "name": "from_folio_id", "type": ["null", "long"]}, {"default": null, "name": "__op", "type": ["null", "string"]}, {"default": null, "name": "__table", "type": ["null", "string"]}, {"default": null, "name": "__source_ts_ms", "type": ["null", "long"]}, {"default": null, "name": "__deleted", "type": ["null", "string"]}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_folios_transactions", "type": "record"}