{"connect.name": "com.cloudbeds.mfd.cdc.a_htl_group_profiles.Value", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "allotment_block_count", "type": ["null", "int"], "default": null}, {"name": "name", "type": "string"}, {"name": "code", "type": "string"}, {"name": "earliest_checkin_at", "type": ["null", {"type": "int", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Date", "logicalType": "date"}], "default": null}, {"name": "latest_checkout_at", "type": ["null", {"type": "int", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Date", "logicalType": "date"}], "default": null}, {"name": "status", "type": "string"}, {"name": "folio_config_id", "type": ["null", "long"], "default": null}, {"name": "transaction_mode", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "transaction_types", "type": ["null", "string"], "default": null}, {"name": "source_code", "type": "string"}, {"name": "total", "type": {"type": "double", "connect.default": 0}, "default": 0}, {"name": "grand_total", "type": {"type": "double", "connect.default": 0}, "default": 0}, {"name": "paid_value", "type": {"type": "double", "connect.default": 0}, "default": 0}, {"name": "created_at", "type": {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}}, {"name": "updated_at", "type": {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}}, {"name": "address_1", "type": "string"}, {"name": "address_2", "type": ["null", "string"], "default": null}, {"name": "city", "type": ["null", "string"], "default": null}, {"name": "state", "type": ["null", "string"], "default": null}, {"name": "country_code", "type": ["null", "string"], "default": null}, {"name": "zip", "type": ["null", "string"], "default": null}, {"name": "commission_type", "type": ["null", "string"], "default": null}, {"name": "commission_amount", "type": ["null", "double"], "default": null}, {"name": "enable_aggregate_allotment_block", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "aggregate_allotment_block_package_id", "type": {"type": "long", "connect.default": 0}, "default": 0}, {"name": "__op", "type": ["null", "string"], "default": null}, {"name": "__table", "type": ["null", "string"], "default": null}, {"name": "__source_ts_ms", "type": ["null", "long"], "default": null}, {"name": "__deleted", "type": ["null", "string"], "default": null}, {"name": "__island", "type": ["null", "string"], "default": null}], "name": "Value", "namespace": "com.cloudbeds.mfd.cdc.a_htl_group_profiles", "type": "record"}