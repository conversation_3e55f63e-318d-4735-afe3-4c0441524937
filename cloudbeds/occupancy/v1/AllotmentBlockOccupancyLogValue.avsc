{"fields": [{"name": "organization_id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "room_type_id", "type": "long"}, {"name": "inventory_kind", "type": "com.cloudbeds.OccupancyService.InventoryKind"}, {"name": "allotment_block_id", "type": "long"}, {"name": "group_profile_id", "type": "long"}, {"name": "stay_date", "type": {"logicalType": "date", "type": "int"}}, {"name": "count", "type": "long"}, {"name": "created_at", "type": ["null", {"logicalType": "timestamp-millis", "type": "long"}]}], "name": "AllotmentBlockOccupancyLogValue", "namespace": "com.cloudbeds.OccupancyService", "type": "record"}