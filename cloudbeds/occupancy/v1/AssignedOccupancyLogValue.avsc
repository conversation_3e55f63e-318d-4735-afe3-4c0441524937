{"fields": [{"name": "organization_id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "room_type_id", "type": "long"}, {"name": "room_id", "type": "string"}, {"name": "inventory_kind", "type": "com.cloudbeds.OccupancyService.InventoryKind"}, {"default": null, "name": "booking_id", "type": ["null", "long"]}, {"default": null, "name": "booking_room_id", "type": ["null", "long"]}, {"name": "status", "type": {"name": "AssignedOccupancyStatus", "symbols": ["SOLD", "BLOCKED", "LINK_BLOCKED", "OUT_OF_SERVICE", "AVAILABLE", "ALLOTMENT_BLOCKED"], "type": "enum"}}, {"name": "is_direct", "type": "boolean"}, {"name": "stay_date", "type": {"logicalType": "date", "type": "int"}}, {"name": "created_at", "type": {"logicalType": "timestamp-millis", "type": "long"}}], "name": "AssignedOccupancyLogValue", "namespace": "com.cloudbeds.OccupancyService", "type": "record"}