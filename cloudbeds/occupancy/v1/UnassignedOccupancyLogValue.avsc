{"fields": [{"name": "organization_id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "room_type_id", "type": "long"}, {"name": "inventory_kind", "type": "com.cloudbeds.OccupancyService.InventoryKind"}, {"name": "status", "type": {"name": "UnassignedOccupancyStatus", "symbols": ["SOLD", "LINK_BLOCKED"], "type": "enum"}}, {"name": "booking_room_id", "type": "long"}, {"name": "booking_id", "type": "long"}, {"name": "stay_date", "type": {"logicalType": "date", "type": "int"}}, {"name": "is_assumed", "type": "boolean"}, {"name": "is_direct", "type": "boolean"}, {"name": "created_at", "type": ["null", {"logicalType": "timestamp-millis", "type": "long"}]}], "name": "UnassignedOccupancyLogValue", "namespace": "com.cloudbeds.OccupancyService", "type": "record"}